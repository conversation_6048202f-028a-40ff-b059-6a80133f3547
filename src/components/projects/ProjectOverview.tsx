// This component has been temporarily disabled due to Supabase type issues
// It will be re-enabled once the database types are properly regenerated

import React from 'react';

interface ProjectOverviewProps {
  project: any;
}

export const ProjectOverview: React.FC<ProjectOverviewProps> = ({ project }) => {
  return (
    <div className="p-8 text-center text-muted-foreground">
      <p>Project overview component temporarily disabled</p>
      <p className="text-sm mt-2">Will be restored once Supabase types are regenerated</p>
    </div>
  );
};