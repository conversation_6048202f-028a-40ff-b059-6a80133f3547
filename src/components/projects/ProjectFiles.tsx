import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';

interface ProjectFilesProps {
  projectId: string;
}

export const ProjectFiles: React.FC<ProjectFilesProps> = ({ projectId }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Files</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-muted-foreground">
          <p>File management interface will be implemented in Phase 2.3</p>
          <p className="text-sm mt-2">Project ID: {projectId}</p>
        </div>
      </CardContent>
    </Card>
  );
};