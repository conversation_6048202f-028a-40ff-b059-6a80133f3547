import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';

interface ProjectMilestonesProps {
  projectId: string;
}

export const ProjectMilestones: React.FC<ProjectMilestonesProps> = ({ projectId }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Milestones</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-muted-foreground">
          <p>Milestone management interface will be implemented in Phase 2.3</p>
          <p className="text-sm mt-2">Project ID: {projectId}</p>
        </div>
      </CardContent>
    </Card>
  );
};