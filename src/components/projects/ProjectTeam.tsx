import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface Project {
  id: string;
  name: string;
  teams?: {
    id: string;
    name: string;
    description?: string;
  };
}

interface ProjectTeamProps {
  project: Project;
}

export const ProjectTeam: React.FC<ProjectTeamProps> = ({ project }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Team</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-muted-foreground">
          <p>Team management interface will be implemented in Phase 2.2</p>
          {project.teams && (
            <p className="text-sm mt-2">
              Current team: {project.teams.name}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};