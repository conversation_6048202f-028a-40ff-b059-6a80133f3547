import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TeamMemberManagement } from './TeamMemberManagement';
import { Calendar, Edit, Users } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface TeamDetailViewProps {
  teamId: string;
  onEdit?: () => void;
}

export const TeamDetailView: React.FC<TeamDetailViewProps> = ({
  teamId,
  onEdit
}) => {
  const { data: team, isLoading, refetch } = useQuery({
    queryKey: ['team', teamId],
    queryFn: async () => {
      console.log('TeamDetailView: Fetching team with ID:', teamId);
      
      try {
        const { data, error } = await (supabase as any)
          .from('teams')
          .select(`
            *,
            team_members(count),
            profiles:created_by (
              first_name,
              last_name
            )
          `)
          .eq('id', teamId)
          .single();
        
        console.log('TeamDetailView: Query result:', { data, error });
        
        if (error) throw error;
        return data;
      } catch (error) {
        console.error('TeamDetailView: Team query failed:', error);
        return null;
      }
    },
  });

  console.log('TeamDetailView: Current state:', { teamId, team, isLoading });

  if (isLoading || !team) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Team Header */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CardTitle className="text-2xl">{team.name}</CardTitle>
              <Badge variant={team.is_active ? 'default' : 'secondary'}>
                {team.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            {team.description && (
              <p className="text-muted-foreground">{team.description}</p>
            )}
          </div>
          {onEdit && (
            <Button onClick={onEdit} variant="outline" className="gap-2">
              <Edit className="w-4 h-4" />
              Edit Team
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {team.team_members?.[0]?.count || 0} members
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Created: {team.created_at ? new Date(team.created_at).toLocaleDateString() : 'Unknown'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Created by: {team.profiles?.first_name} {team.profiles?.last_name}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Members Management */}
      <TeamMemberManagement teamId={teamId} onRefresh={refetch} />
    </div>
  );
};