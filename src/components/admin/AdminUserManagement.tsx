import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';

export const AdminUserManagement: React.FC = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const createAdminUser = async () => {
    if (!email.trim()) {
      toast({
        title: "Error",
        description: "Please enter an email address",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await (supabase as any).rpc('create_admin_user', {
        user_email: email.trim()
      });

      if (error) {
        throw error;
      }

      if (data) {
        toast({
          title: "Success",
          description: `Admin role assigned to ${email}`,
        });
        setEmail('');
      } else {
        toast({
          title: "Error",
          description: "User not found. Make sure the user has signed up first.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error creating admin user:', error);
      toast({
        title: "Error",
        description: "Failed to assign admin role",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Admin User Management</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="admin-email">Promote User to Admin</Label>
          <div className="flex gap-2">
            <Input
              id="admin-email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={createAdminUser}
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Make Admin'}
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Enter the email of an existing user to grant them admin privileges
          </p>
        </div>
      </CardContent>
    </Card>
  );
};